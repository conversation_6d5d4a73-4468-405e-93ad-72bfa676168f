'use client'

import { useState, useEffect } from 'react'
import { ChevronLeft, Filter, Search, Eye, ExternalLink } from 'lucide-react'
import Link from 'next/link'

interface Feature {
  id: string
  feature_id: string
  name: string
  description: string
  category: string
  stage: string
  priority: 'low' | 'medium' | 'high'
  difficulty: 'low' | 'medium' | 'high'
  research_backed: boolean
  stage_impacts: Array<{
    stage: string
    impact_level: 'none' | 'low' | 'medium' | 'high'
    reasoning?: string
  }>
  examples?: Array<{
    company_name: string
    success_story: string
  }>
  mechanics?: string[]
  impact_areas?: string[]
}

interface MatrixData {
  features: Feature[]
  total: number
}

const STAGES = ['acquisition', 'activation', 'retention', 'referral', 'revenue']
const STAGE_COLORS = {
  acquisition: 'bg-blue-500',
  activation: 'bg-green-500', 
  retention: 'bg-purple-500',
  referral: 'bg-pink-500',
  revenue: 'bg-yellow-500'
}

const IMPACT_COLORS = {
  none: 'bg-gray-200 text-gray-600',
  low: 'bg-yellow-200 text-yellow-800',
  medium: 'bg-orange-200 text-orange-800', 
  high: 'bg-red-200 text-red-800'
}

const PRIORITY_COLORS = {
  low: 'bg-gray-100 text-gray-700',
  medium: 'bg-blue-100 text-blue-700',
  high: 'bg-red-100 text-red-700'
}

export default function MatrixPage() {
  const [data, setData] = useState<MatrixData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [priorityFilter, setPriorityFilter] = useState<string>('')
  const [difficultyFilter, setDifficultyFilter] = useState<string>('')
  const [selectedFeature, setSelectedFeature] = useState<Feature | null>(null)

  useEffect(() => {
    fetchMatrixData()
  }, [priorityFilter, difficultyFilter])

  const fetchMatrixData = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      if (priorityFilter) params.append('priority', priorityFilter)
      if (difficultyFilter) params.append('difficulty', difficultyFilter)
      
      const response = await fetch(`/api/analytics/matrix?${params}`)
      if (!response.ok) throw new Error('Failed to fetch matrix data')
      
      const result = await response.json()
      setData(result.data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load data')
    } finally {
      setLoading(false)
    }
  }

  const filteredFeatures = data?.features.filter(feature =>
    feature.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    feature.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    feature.category.toLowerCase().includes(searchTerm.toLowerCase())
  ) || []

  const getImpactForStage = (feature: Feature, stage: string) => {
    const impact = feature.stage_impacts?.find(si => si.stage === stage)
    return impact?.impact_level || 'none'
  }

  const getImpactScore = (feature: Feature) => {
    const scores = { none: 0, low: 1, medium: 2, high: 3 }
    return feature.stage_impacts?.reduce((sum, impact) => 
      sum + scores[impact.impact_level], 0) || 0
  }

  // Sort features by impact score (breadth of impact)
  const sortedFeatures = [...filteredFeatures].sort((a, b) => 
    getImpactScore(b) - getImpactScore(a)
  )

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-neo-cyan mx-auto mb-4"></div>
          <p className="text-gray-600">Loading matrix data...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">Error: {error}</p>
          <button 
            onClick={fetchMatrixData}
            className="btn-primary"
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="flex items-center text-gray-600 hover:text-gray-900">
                <ChevronLeft className="w-5 h-5 mr-1" />
                Back to Home
              </Link>
              <div className="ml-6 h-6 w-px bg-gray-300"></div>
              <h1 className="ml-6 text-xl font-semibold text-gray-900">
                AARRR Impact Matrix
              </h1>
            </div>
          </div>
        </div>
      </header>

      {/* Controls */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Search features..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-neo-cyan focus:border-transparent"
                />
              </div>
            </div>

            {/* Filters */}
            <div className="flex gap-4">
              <select
                value={priorityFilter}
                onChange={(e) => setPriorityFilter(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-neo-cyan focus:border-transparent"
              >
                <option value="">All Priorities</option>
                <option value="high">High Priority</option>
                <option value="medium">Medium Priority</option>
                <option value="low">Low Priority</option>
              </select>

              <select
                value={difficultyFilter}
                onChange={(e) => setDifficultyFilter(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-neo-cyan focus:border-transparent"
              >
                <option value="">All Difficulties</option>
                <option value="low">Low Difficulty</option>
                <option value="medium">Medium Difficulty</option>
                <option value="high">High Difficulty</option>
              </select>
            </div>
          </div>

          <div className="mt-4 text-sm text-gray-600">
            Showing {sortedFeatures.length} features sorted by impact breadth
          </div>
        </div>

        {/* Matrix Table */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Feature
                  </th>
                  {STAGES.map(stage => (
                    <th key={stage} className="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                      <div className="flex flex-col items-center">
                        <div className={`w-3 h-3 rounded-full ${STAGE_COLORS[stage as keyof typeof STAGE_COLORS]} mb-1`}></div>
                        {stage}
                      </div>
                    </th>
                  ))}
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {sortedFeatures.map((feature) => (
                  <tr key={feature.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4">
                      <div className="flex items-start">
                        <div className="flex-1">
                          <div className="text-sm font-medium text-gray-900">
                            {feature.name}
                          </div>
                          <div className="text-sm text-gray-500 mt-1">
                            {feature.description.substring(0, 100)}...
                          </div>
                          <div className="flex items-center gap-2 mt-2">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${PRIORITY_COLORS[feature.priority]}`}>
                              {feature.priority}
                            </span>
                            <span className="text-xs text-gray-500">
                              {feature.category}
                            </span>
                          </div>
                        </div>
                      </div>
                    </td>
                    {STAGES.map(stage => {
                      const impact = getImpactForStage(feature, stage)
                      return (
                        <td key={stage} className="px-3 py-4 text-center">
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${IMPACT_COLORS[impact]}`}>
                            {impact}
                          </span>
                        </td>
                      )
                    })}
                    <td className="px-6 py-4 text-center">
                      <button
                        onClick={() => setSelectedFeature(feature)}
                        className="text-neo-cyan hover:text-neo-cyan-dark"
                      >
                        <Eye className="w-5 h-5" />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Feature Detail Modal */}
      {selectedFeature && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-start mb-4">
                <h2 className="text-xl font-bold text-gray-900">
                  {selectedFeature.name}
                </h2>
                <button
                  onClick={() => setSelectedFeature(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>
              
              <div className="space-y-4">
                <p className="text-gray-700">{selectedFeature.description}</p>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <span className="text-sm font-medium text-gray-500">Category:</span>
                    <p className="text-sm text-gray-900">{selectedFeature.category}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-500">Primary Stage:</span>
                    <p className="text-sm text-gray-900 capitalize">{selectedFeature.stage}</p>
                  </div>
                </div>

                {selectedFeature.examples && selectedFeature.examples.length > 0 && (
                  <div>
                    <h3 className="text-sm font-medium text-gray-500 mb-2">Examples:</h3>
                    {selectedFeature.examples.map((example, idx) => (
                      <div key={idx} className="bg-gray-50 p-3 rounded-lg mb-2">
                        <p className="font-medium text-sm">{example.company_name}</p>
                        <p className="text-sm text-gray-600">{example.success_story}</p>
                      </div>
                    ))}
                  </div>
                )}

                <div className="flex justify-end">
                  <Link
                    href={`/features/${selectedFeature.id}`}
                    className="btn-primary flex items-center"
                  >
                    <ExternalLink className="w-4 h-4 mr-2" />
                    View Full Details
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
