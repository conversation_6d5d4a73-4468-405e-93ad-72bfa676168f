'use client'

import { useState } from 'react'

export default function TestAPIPage() {
  const [result, setResult] = useState<string>('')
  const [loading, setLoading] = useState(false)

  const testAPI = async (endpoint: string, method: string = 'GET', body?: any) => {
    setLoading(true)
    try {
      const options: RequestInit = {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
      }

      if (body) {
        options.body = JSON.stringify(body)
      }

      const response = await fetch(endpoint, options)
      const data = await response.json()
      
      setResult(`${method} ${endpoint}\nStatus: ${response.status}\nResponse:\n${JSON.stringify(data, null, 2)}`)
    } catch (error) {
      setResult(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setLoading(false)
    }
  }

  const testFeatures = () => testAPI('/api/features?limit=5')
  
  const testMatrix = () => testAPI('/api/analytics/matrix')
  
  const testStageImpacts = () => testAPI('/api/stage-impacts')
  
  const testExamples = () => testAPI('/api/examples')

  const testCreateFeature = () => testAPI('/api/features', 'POST', {
    feature_id: 'test_feature_api',
    name: 'Test Feature',
    description: 'This is a test feature created via API',
    category: 'Test Category',
    stage: 'activation',
    priority: 'medium',
    difficulty: 'medium',
    research_backed: true,
    justification: 'This is a test feature for API validation'
  })

  const testCreateStageImpacts = async () => {
    setLoading(true)
    try {
      // First get a feature to add impacts to
      const featuresResponse = await fetch('/api/features?limit=1')
      const featuresResult = await featuresResponse.json()

      if (!featuresResult.data || featuresResult.data.length === 0) {
        setResult('No features found to add stage impacts to')
        setLoading(false)
        return
      }

      const feature = featuresResult.data[0]
      const stages = ['acquisition', 'activation', 'retention', 'referral', 'revenue']
      const impacts = ['high', 'medium', 'low', 'none']

      const results = []

      // Create stage impacts for each stage
      for (const stage of stages) {
        const randomImpact = impacts[Math.floor(Math.random() * impacts.length)]

        const response = await fetch('/api/stage-impacts', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            feature_id: feature.id,
            stage: stage,
            impact_level: randomImpact,
            reasoning: `AI-generated impact assessment for ${stage} stage`
          })
        })

        const result = await response.json()
        results.push(`${stage}: ${result.success ? 'Success' : 'Failed'}`)
      }

      setResult(`Created stage impacts for feature "${feature.name}":\n${results.join('\n')}`)
    } catch (error) {
      setResult(`Error: ${error}`)
    }
    setLoading(false)
  }

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">API Testing Dashboard</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
        <button
          onClick={testFeatures}
          disabled={loading}
          className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded disabled:opacity-50"
        >
          Test GET /api/features
        </button>
        
        <button
          onClick={testMatrix}
          disabled={loading}
          className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded disabled:opacity-50"
        >
          Test GET /api/analytics/matrix
        </button>
        
        <button
          onClick={testStageImpacts}
          disabled={loading}
          className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded disabled:opacity-50"
        >
          Test GET /api/stage-impacts
        </button>
        
        <button
          onClick={testExamples}
          disabled={loading}
          className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded disabled:opacity-50"
        >
          Test GET /api/examples
        </button>
        
        <button
          onClick={testCreateFeature}
          disabled={loading}
          className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded disabled:opacity-50"
        >
          Test POST /api/features
        </button>

        <button
          onClick={testCreateStageImpacts}
          disabled={loading}
          className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded disabled:opacity-50"
        >
          Create Sample Stage Impacts
        </button>
      </div>

      {loading && (
        <div className="mb-4 p-4 bg-blue-100 border border-blue-300 rounded">
          <p className="text-blue-700">Loading...</p>
        </div>
      )}

      {result && (
        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-xl font-semibold mb-2">API Response:</h2>
          <pre className="whitespace-pre-wrap text-sm overflow-auto max-h-96">
            {result}
          </pre>
        </div>
      )}
    </div>
  )
}
